[2025-09-18 06:01:43] local.INFO: Domain History: Domain "hyunta.net" outbound transfer approved from Server. Transfer completed successfully.  
[2025-09-18 06:03:06] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:03:06] local.ERROR: {"query":[],"parameter":[],"error":"TypeError","message":"App\\Modules\\Transfer\\Services\\EppTransferService::sendHttpPostRequest(): Argument #2 ($path) must be of type string, null given, called in C:\\1xampp\\htdocs\\sd-admin\\app\\Modules\\Transfer\\Services\\EppTransferService.php on line 80","code":0}  
[2025-09-18 06:13:41] local.ERROR: Event Unknown transfer action: approve for domain ID: 159  
[2025-09-18 06:13:42] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route transfer\/outbound\/approve\/3. Supported methods: POST.","code":0}  
[2025-09-18 06:14:31] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:14:31] local.ERROR: a@a.a Method Illuminate\Http\Client\PendingRequest::transfer does not exist.  
[2025-09-18 06:14:31] local.ERROR: a@a.a Transfer job failed: Error Unknown  
[2025-09-18 06:14:31] local.INFO: Transfer job attempt: 2/3  
[2025-09-18 06:14:31] local.ERROR: {"query":[],"parameter":[],"error":"App\\Exceptions\\FailedRequestException","message":"Error Unknown","code":520}  
[2025-09-18 06:14:31] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:14:31] local.ERROR: a@a.a Method Illuminate\Http\Client\PendingRequest::transfer does not exist.  
[2025-09-18 06:14:31] local.ERROR: a@a.a Transfer job failed: Error Unknown  
[2025-09-18 06:14:31] local.INFO: Transfer job attempt: 1/3  
[2025-09-18 06:14:31] local.ERROR: {"query":[],"parameter":[],"error":"App\\Exceptions\\FailedRequestException","message":"Error Unknown","code":520}  
[2025-09-18 06:19:33] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:19:36] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:19:37] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:19:37.743+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:19:37.743+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:19:37] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:19:37] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:19:37] local.INFO: Transfer job attempt: 3/3  
[2025-09-18 06:19:37] local.ERROR: a@a.a Transfer job permanently failed after 3 attempts: Retry  
[2025-09-18 06:19:37] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:19:37] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:19:37] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:19:38] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:19:38.963+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:19:38.963+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:19:38] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:19:38] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:19:38] local.INFO: Transfer job attempt: 2/3  
[2025-09-18 06:19:38] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:23:41] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:23:42] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:23:42] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:23:43.684+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:23:43.684+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:23:42] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:23:42] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:23:42] local.INFO: Transfer job attempt: 3/3  
[2025-09-18 06:23:42] local.ERROR: a@a.a Transfer job permanently failed after 3 attempts: Retry  
[2025-09-18 06:23:42] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:24:57] local.INFO: Domain History: Domain "hyunta.com" outbound transfer approved from Server. Transfer completed successfully.  
[2025-09-18 06:24:57] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route transfer\/outbound\/approve\/3. Supported methods: POST.","code":0}  
[2025-09-18 06:25:07] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:25:08] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:25:08] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:25:09.542+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:25:09.542+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:25:08] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:25:08] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:25:08] local.INFO: Transfer job attempt: 1/3  
[2025-09-18 06:25:08] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:27:30] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Class App\\Modules\\Epp\\Services\\LocalEppUrl contains 1 abstract method and must therefore be declared abstract or implement the remaining methods (App\\Modules\\Epp\\Services\\EppUrlInterface::transfer)","code":0}  
[2025-09-18 06:28:47] local.INFO: Domain History: Domain "hyunta.net" outbound transfer approved from Server. Transfer completed successfully.  
[2025-09-18 06:28:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route transfer\/outbound\/approve\/2. Supported methods: POST.","code":0}  
[2025-09-18 06:29:01] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 06:29:01] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:29:02] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:29:02.841+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:29:02.841+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:29:02] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:29:02] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:29:02] local.INFO: Transfer job attempt: 2/3  
[2025-09-18 06:29:02] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 06:29:02] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 06:29:02] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 06:29:02] local.ERROR: HTTP request returned status code 404:
{"timestamp":"2025-09-18T06:29:03.753+00:00","status":404,"error":"Not Found","path":"/v3_1/domain/transfer/approve"}
 ; {"timestamp":"2025-09-18T06:29:03.753+00:00","status":404,"error":"Not Found","path":"\/v3_1\/domain\/transfer\/approve"}  
[2025-09-18 06:29:02] local.ERROR: a@a.a failed to approve the request. Job has been added to the retry logs.  
[2025-09-18 06:29:02] local.ERROR: a@a.a Transfer job failed: Retry  
[2025-09-18 06:29:02] local.INFO: Transfer job attempt: 1/3  
[2025-09-18 06:29:02] local.ERROR: {"query":[],"parameter":[],"error":"Exception","message":"Retry","code":0}  
[2025-09-18 07:08:42] local.INFO: user login from 127.0.0.1  
[2025-09-18 07:08:57] local.INFO: a@a.a Transfer job started for domain: hyunta.com  
[2025-09-18 07:08:59] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 07:09:02] local.INFO: Deleted domain id 159.a@a.a  
[2025-09-18 07:09:02] local.INFO: a@a.a Updated transfer domain status of hyunta.com to outbound.adminApproved  
[2025-09-18 07:09:02] local.INFO: a@a.a Domain transfer user action-approve end...  
[2025-09-18 07:09:02] local.INFO: a@a.a Transfer job completed successfully for domain: hyunta.com  
[2025-09-18 07:09:03] local.INFO: a@a.a Transfer job started for domain: hyunta.net  
[2025-09-18 07:09:03] local.INFO: a@a.a Domain transfer user action-approve start...  
[2025-09-18 07:09:04] local.INFO: Deleted domain id 160.a@a.a  
[2025-09-18 07:09:04] local.INFO: a@a.a Updated transfer domain status of hyunta.net to outbound.adminApproved  
[2025-09-18 07:09:04] local.INFO: a@a.a Domain transfer user action-approve end...  
[2025-09-18 07:09:04] local.INFO: a@a.a Transfer job completed successfully for domain: hyunta.net  
[2025-09-18 08:24:44] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-18 08:29:03] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-18 08:29:34] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-18 08:30:38] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-18 08:31:24] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-18 08:31:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-19 03:41:43] local.INFO: ApprovalDeleteRequest: Running...  
[2025-09-19 03:41:45] local.INFO: ApprovalDeleteRequest: Processed 1 expired requests  
[2025-09-19 03:41:45] local.INFO: ApprovalDeleteRequest: Done  
[2025-09-19 03:49:06] local.INFO: Starting domain deletion job for domain: cravytols.com (ID: 168)  
[2025-09-19 03:49:12] local.ERROR: HTTP request returned status code 403:
{"message":"Authorization error","eppCode":2201,"status":"FORBIDDEN","statusCode":403}
 ; {"message":"Authorization error","eppCode":2201,"status":"FORBIDDEN","statusCode":403}  
[2025-09-19 03:49:14] local.ERROR: Domain deletion job failed for domain: cravytols.com (ID: 168). Error: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "admin_id" of relation "admin_notifications" violates not-null constraint
DETAIL:  Failing row contains (251, null, The delete request for cravytols.com has failed. Please check th..., Domain Deletion Failed, null, domain.pending-delete.view, 2025-09-19 03:49:14, 2025-09-19 03:49:14, important). (Connection: client, SQL: insert into "admin_notifications" ("title", "message", "redirect_url", "importance", "created_at", "updated_at") values (Domain Deletion Failed, The delete request for cravytols.com has failed. Please check the domain status and try again., domain.pending-delete.view, important, 2025-09-19 03:49:14, 2025-09-19 03:49:14))  
[2025-09-19 03:49:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column \"admin_id\" of relation \"admin_notifications\" violates not-null constraint
DETAIL:  Failing row contains (251, null, The delete request for cravytols.com has failed. Please check th..., Domain Deletion Failed, null, domain.pending-delete.view, 2025-09-19 03:49:14, 2025-09-19 03:49:14, important). (Connection: client, SQL: insert into \"admin_notifications\" (\"title\", \"message\", \"redirect_url\", \"importance\", \"created_at\", \"updated_at\") values (Domain Deletion Failed, The delete request for cravytols.com has failed. Please check the domain status and try again., domain.pending-delete.view, important, 2025-09-19 03:49:14, 2025-09-19 03:49:14))","code":"23502"}  
[2025-09-19 03:49:14] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:18] local.ERROR: HTTP request returned status code 403:
{"message":"Authorization error","eppCode":2201,"status":"FORBIDDEN","statusCode":403}
 ; {"message":"Authorization error","eppCode":2201,"status":"FORBIDDEN","statusCode":403}  
[2025-09-19 03:49:19] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "admin_id" of relation "admin_notifications" violates not-null constraint
DETAIL:  Failing row contains (252, null, The delete request for klooty.net has failed. Please check the d..., Domain Deletion Failed, null, domain.pending-delete.view, 2025-09-19 03:49:19, 2025-09-19 03:49:19, important). (Connection: client, SQL: insert into "admin_notifications" ("title", "message", "redirect_url", "importance", "created_at", "updated_at") values (Domain Deletion Failed, The delete request for klooty.net has failed. Please check the domain status and try again., domain.pending-delete.view, important, 2025-09-19 03:49:19, 2025-09-19 03:49:19))  
[2025-09-19 03:49:19] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column \"admin_id\" of relation \"admin_notifications\" violates not-null constraint
DETAIL:  Failing row contains (252, null, The delete request for klooty.net has failed. Please check the d..., Domain Deletion Failed, null, domain.pending-delete.view, 2025-09-19 03:49:19, 2025-09-19 03:49:19, important). (Connection: client, SQL: insert into \"admin_notifications\" (\"title\", \"message\", \"redirect_url\", \"importance\", \"created_at\", \"updated_at\") values (Domain Deletion Failed, The delete request for klooty.net has failed. Please check the domain status and try again., domain.pending-delete.view, important, 2025-09-19 03:49:19, 2025-09-19 03:49:19))","code":"23502"}  
[2025-09-19 03:49:19] local.INFO: Starting domain deletion job for domain: cravytols.com (ID: 168)  
[2025-09-19 03:49:20] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:20] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:20] local.ERROR: Domain deletion job failed for domain: cravytols.com (ID: 168). Error: Undefined array key "data"  
[2025-09-19 03:49:20] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:20] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:21] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:22] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:22] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:49:22] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:22] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:22] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:23] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:23] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:49:23] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:23] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:24] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:24] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:24] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:49:24] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:24] local.INFO: Starting domain deletion job for domain: cravytols.com (ID: 168)  
[2025-09-19 03:49:25] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:25] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:25] local.ERROR: Domain deletion job failed for domain: cravytols.com (ID: 168). Error: Undefined array key "data"  
[2025-09-19 03:49:25] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:25] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:26] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:27] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:27] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:49:27] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:27] local.INFO: Starting domain deletion job for domain: cravytols.com (ID: 168)  
[2025-09-19 03:49:27] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:28] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:28] local.ERROR: Domain deletion job failed for domain: cravytols.com (ID: 168). Error: Undefined array key "data"  
[2025-09-19 03:49:28] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:28] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:28] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:29] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:29] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:49:29] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:29] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:30] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:30] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:30] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:49:30] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:49:30] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:49:31] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:32] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:49:32] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:49:32] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:50:50] local.INFO: user login from 127.0.0.1[2025-09-19 03:55:46] local.INFO: Domain History: Domain deletion request cancelled by admin 1 (a@a.a)  
[2025-09-19 03:55:55] local.INFO: Starting domain deletion job for domain: cravytols.com (ID: 168)  
[2025-09-19 03:55:57] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:55:58] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:55:58] local.ERROR: Domain deletion job failed for domain: cravytols.com (ID: 168). Error: Undefined array key "data"  
[2025-09-19 03:55:58] local.ERROR: Undefined array key "data"  
[2025-09-19 03:55:58] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:55:58] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:55:58] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:55:59] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:55:59] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:55:59] local.ERROR: Undefined array key "data"  
[2025-09-19 03:55:59] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:55:59] local.INFO: Starting domain deletion job for domain: cravytols.com (ID: 168)  
[2025-09-19 03:56:00] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:01] local.ERROR: HTTP request returned status code 400:
{"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"cravytols.com found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:01] local.ERROR: Domain deletion job failed for domain: cravytols.com (ID: 168). Error: Undefined array key "data"  
[2025-09-19 03:56:01] local.ERROR: Undefined array key "data"  
[2025-09-19 03:56:01] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:56:01] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:56:01] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:02] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:02] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:56:02] local.ERROR: Undefined array key "data"  
[2025-09-19 03:56:02] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:56:02] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:56:02] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:03] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:03] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:56:03] local.ERROR: Undefined array key "data"  
[2025-09-19 03:56:03] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 03:56:03] local.INFO: Starting domain deletion job for domain: klooty.net (ID: 173)  
[2025-09-19 03:56:04] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:04] local.ERROR: HTTP request returned status code 400:
{"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}
 ; {"message":"klooty.net found but not available","status":"BAD_REQUEST","statusCode":400}  
[2025-09-19 03:56:04] local.ERROR: Domain deletion job failed for domain: klooty.net (ID: 173). Error: Undefined array key "data"  
[2025-09-19 03:56:04] local.ERROR: Undefined array key "data"  
[2025-09-19 03:56:04] local.ERROR: {"query":[],"parameter":[],"error":"ErrorException","message":"Undefined array key \"data\"","code":0}  
[2025-09-19 04:01:00] local.INFO: ApprovalDeleteRequest: Running...  
[2025-09-19 04:01:00] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-09-19 04:01:00] local.INFO: ApprovalDeleteRequest: Done  
[2025-09-19 04:01:31] local.INFO: ApprovalDeleteRequest: Running...  
[2025-09-19 04:01:31] local.INFO: ApprovalDeleteRequest: Processed 1 expired requests  
[2025-09-19 04:01:31] local.INFO: ApprovalDeleteRequest: Done  
[2025-09-19 04:01:47] local.INFO: Starting domain deletion job for domain: wallqueue.com (ID: 177) - Attempt 1/3  
[2025-09-19 04:01:53] local.INFO: Successfully completed domain deletion job for domain: wallqueue.com  
[2025-09-19 05:10:02] local.INFO: ApprovalDeleteRequest: Running...  
[2025-09-19 05:10:02] local.INFO: ApprovalDeleteRequest: Processed 0 expired requests  
[2025-09-19 05:10:02] local.INFO: ApprovalDeleteRequest: Done  
